{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/IsotopeAI/isotopeai-nextjs/src/components/theme/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  createContext,\n  useCallback,\n  useEffect,\n  useState,\n  type ReactNode,\n} from \"react\";\n\nexport type Theme = \"light\" | \"dark\";\n\nexport interface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nexport const ThemeContext = createContext<ThemeContextType | undefined>(\n  undefined\n);\n\nexport function ThemeProvider({ children }: { children: ReactNode }) {\n  const [theme, setTheme] = useState<Theme>(\"light\");\n\n  useEffect(() => {\n    const storedTheme = localStorage.getItem(\"theme\") as Theme | null;\n    const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\")\n      .matches\n      ? \"dark\"\n      : \"light\";\n    const initialTheme = storedTheme || systemTheme;\n    setTheme(initialTheme);\n  }, []);\n\n  useEffect(() => {\n    if (theme === \"dark\") {\n      document.documentElement.classList.add(\"dark\");\n      localStorage.setItem(\"theme\", \"dark\");\n    } else {\n      document.documentElement.classList.remove(\"dark\");\n      localStorage.setItem(\"theme\", \"light\");\n    }\n  }, [theme]);\n\n  const toggleTheme = useCallback(() => {\n    setTheme((prevTheme) => (prevTheme === \"light\" ? \"dark\" : \"light\"));\n  }, []);\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAiBO,MAAM,6BAAe,IAAA,sNAAa,EACvC;AAGK,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAQ;IAE1C,IAAA,kNAAS,EAAC;QACR,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,MAAM,cAAc,OAAO,UAAU,CAAC,gCACnC,OAAO,GACN,SACA;QACJ,MAAM,eAAe,eAAe;QACpC,SAAS;IACX,GAAG,EAAE;IAEL,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,QAAQ;YACpB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc,IAAA,oNAAW,EAAC;QAC9B,SAAS,CAAC,YAAe,cAAc,UAAU,SAAS;IAC5D,GAAG,EAAE;IAEL,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/IsotopeAI/isotopeai-nextjs/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/IsotopeAI/isotopeai-nextjs/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/IsotopeAI/isotopeai-nextjs/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}