/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M16 8q6 0 6-6-6 0-6 6", key: "qsyyc4" }],
  ["path", { d: "M17.41 3.59a10 10 0 1 0 3 3", key: "41m9h7" }],
  ["path", { d: "M2 2a26.6 26.6 0 0 1 10 20c.9-6.82 1.5-9.5 4-14", key: "qiv7li" }]
];
const Vegan = createLucideIcon("vegan", __iconNode);

export { __iconNode, Vegan as default };
//# sourceMappingURL=vegan.js.map
