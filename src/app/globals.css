@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.7798 0.0064 43.3179);
  --foreground: oklch(0.2466 0.0252 261.0620);
  --card: oklch(0.8251 0.0069 106.5493);
  --card-foreground: oklch(0.2466 0.0252 261.0620);
  --popover: oklch(0.8251 0.0069 106.5493);
  --popover-foreground: oklch(0.2466 0.0252 261.0620);
  --primary: oklch(0.4695 0.2300 272.1442);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.7349 0.0072 53.4085);
  --secondary-foreground: oklch(0.3846 0.0173 258.3722);
  --muted: oklch(0.7798 0.0064 43.3179);
  --muted-foreground: oklch(0.4736 0.0158 262.3278);
  --accent: oklch(0.7773 0.0795 322.0608);
  --accent-foreground: oklch(0.3239 0.0213 262.5594);
  --destructive: oklch(0.5531 0.2011 26.9010);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.7349 0.0072 53.4085);
  --input: oklch(0.7349 0.0072 53.4085);
  --ring: oklch(0.4695 0.2300 272.1442);
  --chart-1: oklch(0.4695 0.2300 272.1442);
  --chart-2: oklch(0.4270 0.2139 275.1302);
  --chart-3: oklch(0.4052 0.1513 280.9926);
  --chart-4: oklch(0.3581 0.1205 280.8668);
  --chart-5: oklch(0.3228 0.0929 281.5113);
  --sidebar: oklch(0.7349 0.0072 53.4085);
  --sidebar-foreground: oklch(0.2466 0.0252 261.0620);
  --sidebar-primary: oklch(0.4695 0.2300 272.1442);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.7773 0.0795 322.0608);
  --sidebar-accent-foreground: oklch(0.3239 0.0213 262.5594);
  --sidebar-border: oklch(0.7349 0.0072 53.4085);
  --sidebar-ring: oklch(0.4695 0.2300 272.1442);
  --font-sans: Plus Jakarta Sans, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Alegreya SC, ui-serif, serif;
  --font-mono: Google Sans Code, ui-monospace, monospace;
  --radius: 1.2rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2189 0.0065 78.1847);
  --foreground: oklch(0.7774 0.0331 255.6129);
  --card: oklch(0.2461 0.0059 56.1018);
  --card-foreground: oklch(0.7774 0.0331 255.6129);
  --popover: oklch(0.2461 0.0059 56.1018);
  --popover-foreground: oklch(0.7774 0.0331 255.6129);
  --primary: oklch(0.8285 0.1965 133.6257);
  --primary-foreground: oklch(0.2189 0.0065 78.1847);
  --secondary: oklch(0.2915 0.0057 56.1650);
  --secondary-foreground: oklch(0.7352 0.0152 254.6341);
  --muted: oklch(0.2461 0.0059 56.1018);
  --muted-foreground: oklch(0.6044 0.0219 260.1569);
  --accent: oklch(0.3356 0.0046 39.4247);
  --accent-foreground: oklch(0.7352 0.0152 254.6341);
  --destructive: oklch(0.5531 0.2011 26.9010);
  --destructive-foreground: oklch(0.2189 0.0065 78.1847);
  --border: oklch(0.2915 0.0057 56.1650);
  --input: oklch(0.2915 0.0057 56.1650);
  --ring: oklch(0.5375 0.2033 272.9443);
  --chart-1: oklch(0.5375 0.2033 272.9443);
  --chart-2: oklch(0.4695 0.2300 272.1442);
  --chart-3: oklch(0.4270 0.2139 275.1302);
  --chart-4: oklch(0.4052 0.1513 280.9926);
  --chart-5: oklch(0.3581 0.1205 280.8668);
  --sidebar: oklch(0.2915 0.0057 56.1650);
  --sidebar-foreground: oklch(0.7774 0.0331 255.6129);
  --sidebar-primary: oklch(0.5375 0.2033 272.9443);
  --sidebar-primary-foreground: oklch(0.2189 0.0065 78.1847);
  --sidebar-accent: oklch(0.3356 0.0046 39.4247);
  --sidebar-accent-foreground: oklch(0.7352 0.0152 254.6341);
  --sidebar-border: oklch(0.2915 0.0057 56.1650);
  --sidebar-ring: oklch(0.5375 0.2033 272.9443);
  --font-sans: Plus Jakarta Sans, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Alegreya SC, ui-serif, serif;
  --font-mono: Google Sans Code, ui-monospace, monospace;
  --radius: 1.2rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
